#!/usr/bin/env python3
"""
测试SceneLeapPlusDatasetCached数据集的初始化和样本分析
使用配置文件中的参数，mode选择camera_centric_scene_mean_normalized
"""

import os
import sys
import logging
import time
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_sample(sample, sample_idx):
    """分析单个样本的详细信息"""
    logger.info(f"\n=== 样本 {sample_idx} 分析 ===")
    
    if 'error' in sample:
        logger.error(f"样本 {sample_idx} 包含错误: {sample['error']}")
        return
    
    # 分析各个字段
    for key, value in sample.items():
        if isinstance(value, torch.Tensor):
            logger.info(f"{key}: shape={value.shape}, dtype={value.dtype}")
            if key == 'scene_pc':
                logger.info(f"  - 点云数据: {value.shape[0]} 个点，每个点 {value.shape[1]} 维 (xyz+rgb)")
                logger.info(f"  - xyz范围: [{value[:, :3].min():.4f}, {value[:, :3].max():.4f}]")
                logger.info(f"  - rgb范围: [{value[:, 3:].min():.4f}, {value[:, 3:].max():.4f}]")
            elif key == 'hand_model_pose':
                logger.info(f"  - 手部姿态: {value.shape[0]} 个抓取，每个 {value.shape[1]} 维")
                logger.info(f"  - 姿态范围: [{value.min():.4f}, {value.max():.4f}]")
            elif key == 'se3':
                logger.info(f"  - SE3变换矩阵: {value.shape[0]} 个抓取，{value.shape[1]}x{value.shape[2]} 矩阵")
            elif key == 'obj_verts':
                logger.info(f"  - 物体顶点: {value.shape[0]} 个顶点，{value.shape[1]} 维坐标")
            elif key == 'obj_faces':
                logger.info(f"  - 物体面片: {value.shape[0]} 个面片，每个面片 {value.shape[1]} 个顶点")
            elif key == 'object_mask':
                logger.info(f"  - 物体掩码: {value.shape[0]} 个点，{value.sum()} 个物体点")
        elif isinstance(value, list):
            logger.info(f"{key}: 列表，长度={len(value)}")
            if key == 'negative_prompts':
                logger.info(f"  - 负面提示词: {value}")
        elif isinstance(value, str):
            logger.info(f"{key}: '{value}'")
        elif isinstance(value, (int, float)):
            logger.info(f"{key}: {value}")
        else:
            logger.info(f"{key}: {type(value)} - {value}")

def main():
    """主函数"""
    logger.info("开始测试SceneLeapPlusDatasetCached数据集")
    
    # 使用配置文件中的参数
    root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed"
    succ_grasp_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
    obj_root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
    
    # 数据集参数（基于配置文件）
    dataset_params = {
        'root_dir': root_dir,
        'succ_grasp_dir': succ_grasp_dir,
        'obj_root_dir': obj_root_dir,
        'num_grasps': 4,  # 配置文件中的训练参数
        'mode': 'camera_centric_scene_mean_normalized',  # 指定的mode
        'max_grasps_per_object': 160,  # 测试时使用较小值
        'mesh_scale': 0.1,
        'num_neg_prompts': 4,
        'enable_cropping': True,
        'max_points': 10000,
        'grasp_sampling_strategy': 'farthest_point',  # 配置文件中的策略
        'use_exhaustive_sampling': True,  # 配置文件中启用
        'exhaustive_sampling_strategy': 'chunk_farthest_point',  # 配置文件中的策略
        'cache_version': 'v2.0_plus_test_analysis',  # 测试专用版本
        'cache_mode': 'train'
    }
    
    logger.info("数据集参数:")
    for key, value in dataset_params.items():
        logger.info(f"  {key}: {value}")
    
    try:
        # 创建数据集
        logger.info("\n创建SceneLeapPlusDatasetCached数据集...")
        start_time = time.time()
        
        dataset = SceneLeapPlusDatasetCached(**dataset_params)
        
        creation_time = time.time() - start_time
        logger.info(f"数据集创建完成，耗时: {creation_time:.2f}秒")
        logger.info(f"数据集大小: {len(dataset)}")
        
        if len(dataset) == 0:
            logger.error("数据集为空，请检查数据路径")
            return
        
        # 分析前几个样本
        num_samples_to_analyze = min(3, len(dataset))
        logger.info(f"\n开始分析前 {num_samples_to_analyze} 个样本...")
        
        for i in range(num_samples_to_analyze):
            logger.info(f"\n正在加载样本 {i}...")
            start_time = time.time()
            
            sample = dataset[i]
            
            load_time = time.time() - start_time
            logger.info(f"样本 {i} 加载完成，耗时: {load_time:.4f}秒")
            
            # 分析样本
            analyze_sample(sample, i)
        
        # 统计信息
        logger.info(f"\n=== 数据集统计信息 ===")
        logger.info(f"总样本数: {len(dataset)}")
        logger.info(f"缓存状态: {'已加载' if dataset.cache_loaded else '未加载'}")
        logger.info(f"缓存版本: {dataset.cache_version}")
        logger.info(f"坐标系模式: {dataset.coordinate_system_mode}")
        logger.info(f"抓取采样策略: {dataset.grasp_sampling_strategy}")
        logger.info(f"穷尽采样: {'启用' if dataset.use_exhaustive_sampling else '禁用'}")
        if dataset.use_exhaustive_sampling:
            logger.info(f"穷尽采样策略: {dataset.exhaustive_sampling_strategy}")
        
        logger.info("\n测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
